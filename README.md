# 💧 Water Intake Calculator

A modern, responsive Next.js application that calculates daily water intake needs based on weight, exercise duration, and climate conditions. Features Google AdSense integration and real-time calculations.

## 🚀 Features

- **Real-time Calculations**: Instant water intake recommendations as you type
- **Dark/Light Mode**: Automatic theme detection with manual toggle and smooth transitions
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **TypeScript**: Full type safety throughout the application
- **Google AdSense Integration**: Ready-to-use ad components
- **SEO Optimized**: Comprehensive meta tags, structured data, FAQ section, and sitemap
- **PWA Ready**: Manifest file and app icons for progressive web app functionality
- **Turbopack**: Fast development with Next.js 14 and Turbopack
- **Accessible**: ARIA labels, semantic HTML, and keyboard navigation support

## 📋 Scientific Calculation Formula

The water intake calculation is based on **National Academy of Sciences** recommendations and peer-reviewed research:

### **Base Intake**: 35ml per kg body weight
- Based on NAS Adequate Intake guidelines
- Accounts for individual metabolic needs
- Conservative estimate for sedentary adults

### **Exercise Addition**: +0.6L per hour of exercise
- Based on sweat loss research during moderate activity
- Accounts for increased fluid needs during physical activity
- Moderate estimate for typical exercise intensity

### **Climate Addition**: +1.0L for hot/humid conditions
- Based on thermoregulation research
- Accounts for increased fluid losses in hot environments
- Reflects additional needs for temperature regulation

### **Scientific Sources:**
- U.S. National Academy of Sciences Dietary Reference Intakes
- Mayo Clinic hydration guidelines
- Peer-reviewed exercise physiology research
- Thermoregulation and sweat loss studies

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS
- **Development**: Turbopack for fast builds
- **Ads**: Google AdSense integration

## 📁 Project Structure

```
water-intake-calculator/
├── src/
│   ├── app/
│   │   ├── layout.tsx          # Global layout with AdSense script
│   │   ├── page.tsx            # Main calculator page
│   │   └── globals.css         # Global styles
│   └── components/
│       └── Ad.tsx              # Reusable AdSense component
├── public/
│   └── ads.txt                 # AdSense publisher verification
├── .env.local                  # Environment variables
└── [configuration files]
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone or download the project**
   ```bash
   cd water-intake-calculator
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure AdSense (Optional)**
   - Update `.env.local` with your AdSense client ID
   - Update `public/ads.txt` with your publisher information
   - Replace ad slot IDs in components

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🔧 Configuration

### Environment Variables

Create or update `.env.local`:

```env
# Google AdSense Configuration
NEXT_PUBLIC_ADSENSE_CLIENT=ca-pub-your-actual-client-id
```

### AdSense Setup

1. **Get your AdSense client ID** from Google AdSense dashboard
2. **Update environment variables** in `.env.local`
3. **Update ads.txt** in the `public/` directory:
   ```
   google.com, pub-your-publisher-id, DIRECT, f08c47fec0942fa0
   ```
4. **Replace ad slot IDs** in the `Ad` component usage

### Customizing Ad Placements

The `Ad` component accepts these props:

```tsx
<Ad
  adSlot="your-ad-slot-id"     // Required: Your ad slot ID
  adClient="ca-pub-..."        // Optional: Defaults to env variable
  adFormat="auto"              // Optional: Ad format
  className="my-4"             // Optional: CSS classes
  style={{ minHeight: '250px' }} // Optional: Inline styles
/>
```

## 🎨 Customization

### Styling

The app uses Tailwind CSS for styling. Key customization points:

- **Colors**: Update the gradient classes in `layout.tsx` and `page.tsx`
- **Typography**: Modify font classes throughout components
- **Layout**: Adjust container max-widths and spacing
- **Components**: Customize form inputs, buttons, and result displays

### Calculation Logic

To modify the water intake formula, update the `calculateWaterIntake` function in `src/app/page.tsx`:

```typescript
const calculateWaterIntake = (data: FormData): CalculationResult => {
  // Modify these calculations as needed
  const baseIntake = data.weight * 0.033;
  const exerciseBonus = (data.exerciseHours / 0.5) * 0.35;
  const climateBonus = data.climate === 'hot' ? 0.5 : 0;
  // ...
};
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Push to GitHub/GitLab/Bitbucket**
2. **Connect to Vercel**
3. **Add environment variables** in Vercel dashboard
4. **Deploy**

### Other Platforms

The app works on any platform that supports Next.js:

- **Netlify**: Use `@netlify/plugin-nextjs`
- **Railway**: Direct deployment support
- **AWS Amplify**: Next.js SSR support
- **Docker**: Use the included Next.js Docker support

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## ⚠️ Disclaimer

This calculator provides general recommendations for water intake. Always consult with healthcare professionals for personalized medical advice.

## 🆘 Support

If you encounter any issues:

1. Check the [Issues](../../issues) page
2. Ensure all dependencies are installed correctly
3. Verify environment variables are set properly
4. Check browser console for errors

---

**Built with ❤️ using Next.js 14, TypeScript, and Tailwind CSS**
# water-intake-calculator
