'use client';

import { useEffect, useRef } from 'react';

interface AdProps {
  /**
   * AdSense ad slot ID - replace with your actual ad slot ID
   */
  adSlot: string;
  /**
   * AdSense client ID - automatically pulled from environment variables
   */
  adClient?: string;
  /**
   * Ad format (optional) - defaults to 'auto'
   */
  adFormat?: string;
  /**
   * CSS class name for styling
   */
  className?: string;
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
}

// AdSense global interface
declare global {
  interface Window {
    adsbygoogle: Array<Record<string, unknown>>;
  }
}

/**
 * Reusable Google AdSense component
 * 
 * Usage:
 * <Ad adSlot="1234567890" className="my-4" />
 * 
 * Make sure to:
 * 1. Replace adSlot with your actual ad slot ID
 * 2. Set NEXT_PUBLIC_ADSENSE_CLIENT in .env.local
 * 3. Include the AdSense script in layout.tsx
 */
export default function Ad({
  adSlot,
  adClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT,
  adFormat = 'auto',
  className = '',
  style = {}
}: AdProps) {
  const adRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadAd = () => {
      try {
        // Ensure the ad container has proper dimensions
        if (adRef.current && typeof window !== 'undefined') {
          const container = adRef.current;
          const rect = container.getBoundingClientRect();

          // Only load ad if container has width
          if (rect.width > 0) {
            // Initialize adsbygoogle array if it doesn't exist
            window.adsbygoogle = window.adsbygoogle || [];
            // Push the ad configuration
            window.adsbygoogle.push({});
          } else {
            // Retry after a short delay if container has no width
            setTimeout(loadAd, 100);
          }
        }
      } catch (error) {
        console.error('AdSense error:', error);
      }
    };

    // Delay ad loading to ensure DOM is fully rendered
    const timer = setTimeout(loadAd, 100);

    return () => clearTimeout(timer);
  }, []);

  // Don't render if client ID is not available
  if (!adClient) {
    return (
      <div className={`bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 p-4 text-center text-gray-500 dark:text-gray-400 transition-colors duration-200 ${className}`}>
        <p className="text-sm font-medium">Advertisement Space</p>
        <p className="text-xs mt-1">Configure NEXT_PUBLIC_ADSENSE_CLIENT</p>
        <p className="text-xs mt-2 text-gray-400 dark:text-gray-500">Ads help keep this calculator free</p>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Ad Label for Compliance */}
      <div className="text-xs text-gray-400 dark:text-gray-500 text-center mb-2 uppercase tracking-wide">
        Advertisement
      </div>

      <div
        ref={adRef}
        className="bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 transition-colors duration-200"
        style={{
          minWidth: '300px',
          minHeight: '250px',
          width: '100%',
          ...style
        }}
      >
        <ins
          className="adsbygoogle"
          style={{
            display: 'block',
            width: '100%',
            height: 'auto',
            minHeight: '250px'
          }}
          data-ad-client={adClient}
          data-ad-slot={adSlot}
          data-ad-format={adFormat}
          data-full-width-responsive="true"
          data-ad-test={process.env.NODE_ENV === 'development' ? 'on' : undefined}
        />
      </div>

      {/* Spacing for better UX */}
      <div className="text-xs text-gray-400 dark:text-gray-500 text-center mt-2">
        Ads help keep this calculator free and accessible
      </div>
    </div>
  );
}
