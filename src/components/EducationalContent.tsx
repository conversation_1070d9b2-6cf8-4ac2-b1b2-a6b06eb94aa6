interface ScientificReferenceProps {
  title: string;
  description: string;
  url?: string;
  className?: string;
}

export function ScientificReference({ title, description, url, className = "" }: ScientificReferenceProps) {
  const content = (
    <div className={`p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg ${className}`}>
      <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">📚 {title}</h4>
      <p className="text-blue-700 dark:text-blue-300 text-sm">{description}</p>
    </div>
  );

  if (url) {
    return (
      <a href={url} target="_blank" rel="noopener noreferrer" className="block hover:opacity-80 transition-opacity duration-200">
        {content}
      </a>
    );
  }

  return content;
}

interface HealthTipProps {
  icon: string;
  title: string;
  description: string;
  className?: string;
}

export function HealthTip({ icon, title, description, className = "" }: HealthTipProps) {
  return (
    <div className={`p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg ${className}`}>
      <div className="flex items-start space-x-3">
        <span className="text-2xl flex-shrink-0">{icon}</span>
        <div>
          <h4 className="font-semibold text-green-800 dark:text-green-200 mb-1">{title}</h4>
          <p className="text-green-700 dark:text-green-300 text-sm">{description}</p>
        </div>
      </div>
    </div>
  );
}

interface WarningBoxProps {
  title: string;
  description: string;
  className?: string;
}

export function WarningBox({ title, description, className = "" }: WarningBoxProps) {
  return (
    <div className={`p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg ${className}`}>
      <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">⚠️ {title}</h4>
      <p className="text-yellow-700 dark:text-yellow-300 text-sm">{description}</p>
    </div>
  );
}

interface InfoBoxProps {
  title: string;
  description: string;
  icon?: string;
  className?: string;
}

export function InfoBox({ title, description, icon = "ℹ️", className = "" }: InfoBoxProps) {
  return (
    <div className={`p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg ${className}`}>
      <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">{icon} {title}</h4>
      <p className="text-gray-700 dark:text-gray-300 text-sm">{description}</p>
    </div>
  );
}

interface HydrationFactProps {
  fact: string;
  source?: string;
  className?: string;
}

export function HydrationFact({ fact, source, className = "" }: HydrationFactProps) {
  return (
    <div className={`p-3 bg-cyan-50 dark:bg-cyan-900/20 border-l-4 border-cyan-400 dark:border-cyan-600 ${className}`}>
      <p className="text-cyan-800 dark:text-cyan-200 text-sm font-medium">{fact}</p>
      {source && (
        <p className="text-cyan-600 dark:text-cyan-400 text-xs mt-1">— {source}</p>
      )}
    </div>
  );
}

export function HydrationTipsSection() {
  const tips = [
    {
      icon: "🌅",
      title: "Start Your Day with Water",
      description: "Drink a glass of water when you wake up to kickstart hydration after hours without fluids."
    },
    {
      icon: "⏰",
      title: "Set Regular Reminders",
      description: "Use phone alarms or apps to remind yourself to drink water throughout the day."
    },
    {
      icon: "🍎",
      title: "Eat Water-Rich Foods",
      description: "Include fruits and vegetables like watermelon, cucumber, and oranges in your diet."
    },
    {
      icon: "🏃‍♂️",
      title: "Pre-Exercise Hydration",
      description: "Drink 16-20 oz of water 2-3 hours before exercise, and 8 oz 15-20 minutes before."
    },
    {
      icon: "🌡️",
      title: "Monitor Urine Color",
      description: "Pale yellow indicates good hydration; dark yellow suggests you need more fluids."
    },
    {
      icon: "🥤",
      title: "Flavor Your Water",
      description: "Add lemon, cucumber, or mint to make water more appealing if you struggle with plain water."
    }
  ];

  return (
    <section className="mb-8">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
        💡 Hydration Tips & Best Practices
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {tips.map((tip, index) => (
          <HealthTip
            key={index}
            icon={tip.icon}
            title={tip.title}
            description={tip.description}
          />
        ))}
      </div>
    </section>
  );
}

export function ScientificBasisSection() {
  return (
    <section className="mb-8">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
        🔬 Scientific Foundation
      </h2>
      <div className="space-y-4">
        <ScientificReference
          title="National Academy of Sciences Guidelines"
          description="Our calculations are based on the Adequate Intake (AI) recommendations from the National Academy of Sciences, Engineering, and Medicine - the gold standard for hydration guidance in the United States."
          url="https://www.nationalacademies.org/our-work/dietary-reference-intakes-for-electrolytes-and-water"
        />
        
        <ScientificReference
          title="Exercise Physiology Research"
          description="Exercise adjustments are based on peer-reviewed research on sweat rates and fluid replacement during physical activity, particularly from the American College of Sports Medicine."
          url="https://www.acsm.org/docs/default-source/files-for-resource-library/exercise-and-fluid-replacement.pdf"
        />
        
        <ScientificReference
          title="Climate Adaptation Studies"
          description="Hot climate adjustments are derived from research on thermoregulation and fluid requirements in warm environments, ensuring accurate recommendations for different climates."
        />
      </div>
    </section>
  );
}

export function HydrationFactsSection() {
  const facts = [
    {
      fact: "The human body is approximately 60% water, with the brain being about 75% water.",
      source: "Mayo Clinic"
    },
    {
      fact: "Even mild dehydration (2% body weight loss) can impair cognitive performance and mood.",
      source: "British Journal of Nutrition, 2011"
    },
    {
      fact: "Thirst is not always a reliable indicator of hydration needs, especially in older adults.",
      source: "American Journal of Clinical Nutrition"
    },
    {
      fact: "About 20% of daily fluid intake comes from food, particularly fruits and vegetables.",
      source: "National Academy of Sciences"
    },
    {
      fact: "Athletes can lose 1-3 liters of sweat per hour during intense exercise in hot conditions.",
      source: "Sports Medicine Research"
    }
  ];

  return (
    <section className="mb-8">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
        🧠 Hydration Facts
      </h2>
      <div className="space-y-3">
        {facts.map((item, index) => (
          <HydrationFact
            key={index}
            fact={item.fact}
            source={item.source}
          />
        ))}
      </div>
    </section>
  );
}

export function HealthWarningsSection() {
  return (
    <section className="mb-8">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
        ⚕️ Important Health Information
      </h2>
      <div className="space-y-4">
        <WarningBox
          title="When to Consult a Healthcare Provider"
          description="If you have kidney disease, heart conditions, diabetes, take medications affecting fluid balance, are pregnant/breastfeeding, or have a history of electrolyte imbalances, consult your healthcare provider before making significant changes to your hydration habits."
        />
        
        <InfoBox
          title="Signs of Dehydration"
          description="Watch for symptoms like dark urine, fatigue, dizziness, dry mouth, headache, or decreased urination. Severe dehydration requires immediate medical attention."
          icon="🚨"
        />
        
        <InfoBox
          title="Overhydration Risk"
          description="While rare, drinking excessive amounts of water can lead to hyponatremia (low sodium levels). Follow recommended guidelines and listen to your body."
          icon="⚖️"
        />
      </div>
    </section>
  );
}

interface QuickStatsProps {
  totalIntake: number;
  weight: number;
  exerciseHours: number;
}

export function QuickStats({ totalIntake, weight, exerciseHours }: QuickStatsProps) {
  const cupsEquivalent = Math.round(totalIntake * 4.227); // 1 liter ≈ 4.227 cups
  const bottlesEquivalent = Math.round(totalIntake * 2); // 1 liter ≈ 2 standard 500ml bottles
  const dailyPercentage = Math.round((totalIntake / (weight * 0.035)) * 100);

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">{cupsEquivalent}</div>
        <div className="text-xs text-gray-600 dark:text-gray-400">Cups (8 oz)</div>
      </div>
      <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="text-lg font-semibold text-green-600 dark:text-green-400">{bottlesEquivalent}</div>
        <div className="text-xs text-gray-600 dark:text-gray-400">Bottles (500ml)</div>
      </div>
      <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="text-lg font-semibold text-purple-600 dark:text-purple-400">{dailyPercentage}%</div>
        <div className="text-xs text-gray-600 dark:text-gray-400">Above baseline</div>
      </div>
      <div className="text-center p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="text-lg font-semibold text-orange-600 dark:text-orange-400">{exerciseHours}h</div>
        <div className="text-xs text-gray-600 dark:text-gray-400">Exercise time</div>
      </div>
    </div>
  );
}
