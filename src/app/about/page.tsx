import type { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "About - Water Intake Calculator",
  description: "Learn about our science-based water intake calculator, its methodology, and the team behind this free hydration tool.",
  robots: {
    index: true,
    follow: true,
  },
};

export default function About() {
  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <header className="text-center mb-8">
          <div>
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-800 dark:text-gray-100 mb-4 transition-colors duration-200">
              About Our Calculator
            </h1>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-2xl mx-auto transition-colors duration-200">
              Learn about the science, methodology, and mission behind our evidence-based water intake calculator.
            </p>
            <div className="mt-4">
              <Link 
                href="/"
                className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200"
              >
                ← Back to Calculator
              </Link>
            </div>
          </div>
        </header>

        {/* Content */}
        <div className="space-y-8">
          {/* Mission Statement */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Our Mission</h2>
            <p className="text-gray-700 dark:text-gray-300 mb-4 text-lg">
              To provide accurate, science-based hydration guidance that helps people maintain optimal health through proper water intake. We believe that everyone deserves access to reliable, evidence-based health information.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-3xl mb-2">🔬</div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Science-Based</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Evidence from peer-reviewed research</p>
              </div>
              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-3xl mb-2">🆓</div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Always Free</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">No hidden costs or premium features</p>
              </div>
              <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="text-3xl mb-2">👥</div>
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">User-Focused</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Designed for real people's needs</p>
              </div>
            </div>
          </section>

          {/* Scientific Methodology */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Scientific Methodology</h2>
            
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">Foundation: National Academy of Sciences</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Our calculator is built on the Adequate Intake (AI) recommendations from the National Academy of Sciences, Engineering, and Medicine. These guidelines represent the gold standard for hydration recommendations in the United States.
                </p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Key Reference Values:</h4>
                  <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                    <li>• Men (19+ years): 3.7L total water/day (includes food + beverages)</li>
                    <li>• Women (19+ years): 2.7L total water/day (includes food + beverages)</li>
                    <li>• Approximately 80% from fluids, 20% from food</li>
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">Body Weight Correlation</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Research consistently shows that hydration needs correlate with body weight. Our calculator uses the scientifically-supported ratio of 35ml per kilogram of body weight as a baseline for sedentary adults.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">Exercise Adjustments</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Physical activity increases fluid needs due to sweat loss. Based on exercise physiology research, we add 0.6 liters per hour of moderate exercise, which accounts for typical sweat rates during physical activity.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">Climate Considerations</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Hot and humid environments increase fluid requirements for thermoregulation. Our calculator adds 1.0 liter for hot climate conditions, based on research on fluid needs in warm environments.
                </p>
              </div>
            </div>
          </section>

          {/* Key Features */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Key Features</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">🎯 Personalized Calculations</h3>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  Tailored recommendations based on your individual weight, exercise habits, and environmental conditions.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">⚡ Real-Time Results</h3>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  Instant calculations as you adjust your inputs, with detailed breakdowns of each component.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">📱 Mobile-Friendly</h3>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  Responsive design that works perfectly on all devices, from smartphones to desktop computers.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">🌙 Dark Mode</h3>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  Eye-friendly dark mode option with system preference detection for comfortable viewing.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">🔒 Privacy-First</h3>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  No personal data storage - all calculations happen in your browser for maximum privacy.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">📚 Educational Content</h3>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  Comprehensive information about hydration science, tips, and frequently asked questions.
                </p>
              </div>
            </div>
          </section>

          {/* Scientific References */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Scientific References</h2>
            
            <div className="space-y-4 text-sm">
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Primary Sources:</h4>
                <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                  <li>• National Academy of Sciences, Engineering, and Medicine. (2005). Dietary Reference Intakes for Water, Potassium, Sodium, Chloride, and Sulfate.</li>
                  <li>• Mayo Clinic. Water: How much should you drink every day?</li>
                  <li>• American College of Sports Medicine. Exercise and fluid replacement position stand.</li>
                  <li>• Institute of Medicine. Dietary Reference Intakes for Electrolytes and Water.</li>
                </ul>
              </div>
              
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Supporting Research:</h4>
                <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                  <li>• Ganio, M.S., et al. (2011). Mild dehydration impairs cognitive performance and mood. British Journal of Nutrition.</li>
                  <li>• Cheuvront, S.N., & Kenefick, R.W. (2014). Dehydration: physiology, assessment, and performance effects. Comprehensive Physiology.</li>
                  <li>• Armstrong, L.E., et al. (2012). Mild dehydration affects mood in healthy young women. Journal of Nutrition.</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Team & Development */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Development & Expertise</h2>

            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">Our Approach</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  This calculator was developed by health and technology professionals who recognized the need for accessible, accurate hydration guidance. We combine expertise in nutrition science, exercise physiology, and web development to create tools that make health information more accessible.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-3">Quality Assurance</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Every calculation and recommendation is verified against multiple scientific sources. We regularly review and update our methodology to reflect the latest research in hydration science.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-green-600 dark:text-green-400">✓</div>
                    <span className="text-gray-700 dark:text-gray-300 text-sm">Peer-reviewed sources only</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-green-600 dark:text-green-400">✓</div>
                    <span className="text-gray-700 dark:text-gray-300 text-sm">Regular methodology updates</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-green-600 dark:text-green-400">✓</div>
                    <span className="text-gray-700 dark:text-gray-300 text-sm">Cross-referenced calculations</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-green-600 dark:text-green-400">✓</div>
                    <span className="text-gray-700 dark:text-gray-300 text-sm">User feedback integration</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Limitations & Disclaimers */}
          <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Important Limitations</h2>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">⚠️ Medical Disclaimer</h3>
              <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                This calculator provides general guidance based on population averages and should not replace professional medical advice. Individual needs may vary significantly.
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">When to Consult a Healthcare Provider:</h4>
                <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                  <li>Kidney disease or kidney stones</li>
                  <li>Heart conditions or blood pressure issues</li>
                  <li>Diabetes or other metabolic conditions</li>
                  <li>Taking medications that affect fluid balance</li>
                  <li>Pregnancy or breastfeeding</li>
                  <li>History of electrolyte imbalances</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Factors Not Included in Our Calculator:</h4>
                <ul className="list-disc pl-6 text-gray-700 dark:text-gray-300 space-y-1">
                  <li>Individual sweat rates and composition</li>
                  <li>Specific medical conditions</li>
                  <li>Medication effects on hydration</li>
                  <li>Altitude and air pressure changes</li>
                  <li>Individual metabolic differences</li>
                  <li>Specific dietary factors</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Contact & Feedback */}
          <section className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl shadow-lg dark:shadow-2xl p-6 sm:p-8 border border-blue-100 dark:border-blue-800/30 transition-colors duration-200">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">Get Involved</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Share Feedback</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm">
                  Help us improve by sharing your experience, suggestions, or questions about the calculator.
                </p>
                <Link
                  href="/contact"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200 text-sm"
                >
                  Contact Us →
                </Link>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Stay Updated</h3>
                <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm">
                  We regularly update our methodology and add new features based on the latest research.
                </p>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Follow us for updates on hydration science and calculator improvements.
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
