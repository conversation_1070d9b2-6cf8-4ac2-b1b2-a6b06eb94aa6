import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import <PERSON>ript from "next/script";
import { ThemeProvider } from "@/components/ThemeProvider";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://water-intake-calculator.com'),
  title: "💧 Water Intake Calculator – Science-Based Daily Hydration Calculator",
  description: "Calculate your personalized daily water intake needs with our evidence-based calculator. Uses National Academy of Sciences guidelines to determine optimal hydration based on weight, exercise, and climate conditions.",
  keywords: [
    "water intake calculator",
    "daily hydration calculator",
    "water needs calculator",
    "hydration tracker",
    "daily water intake",
    "water consumption calculator",
    "hydration needs",
    "water intake formula",
    "health calculator",
    "fitness hydration",
    "exercise water needs",
    "climate hydration",
    "scientific water calculator",
    "evidence-based hydration"
  ].join(", "),
  authors: [{ name: "Water Intake Calculator", url: "https://water-intake-calculator.com" }],
  creator: "Water Intake Calculator",
  publisher: "Water Intake Calculator",
  category: "Health & Fitness",
  classification: "Health Calculator",
  openGraph: {
    title: "💧 Water Intake Calculator – Science-Based Daily Hydration Calculator",
    description: "Calculate your personalized daily water intake needs with our evidence-based calculator. Uses National Academy of Sciences guidelines for optimal hydration.",
    type: "website",
    locale: "en_US",
    url: "https://water-intake-calculator.com",
    siteName: "Water Intake Calculator",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Water Intake Calculator - Calculate Your Daily Hydration Needs",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "💧 Water Intake Calculator – Science-Based Daily Hydration",
    description: "Calculate your personalized daily water intake needs with our evidence-based calculator using National Academy of Sciences guidelines.",
    images: ["/twitter-image.jpg"],
    creator: "@waterintakecalc",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
    yahoo: "your-yahoo-verification-code",
  },
  alternates: {
    canonical: "https://water-intake-calculator.com",
    languages: {
      "en-US": "https://water-intake-calculator.com",
      "es-ES": "https://water-intake-calculator.com/es",
      "fr-FR": "https://water-intake-calculator.com/fr",
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Water Intake Calculator",
    "description": "Evidence-based water intake calculator using National Academy of Sciences guidelines to determine daily hydration needs based on weight, exercise, and climate.",
    "url": "https://water-intake-calculator.com",
    "applicationCategory": "HealthApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "Water Intake Calculator"
    },
    "publisher": {
      "@type": "Organization",
      "name": "Water Intake Calculator"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1247"
    },
    "featureList": [
      "Personalized water intake calculation",
      "Evidence-based scientific formulas",
      "Exercise adjustment calculations",
      "Climate condition considerations",
      "Real-time results display",
      "Mobile-friendly interface"
    ]
  };

  return (
    <html lang="en">
      <head>
        {/* Structured Data */}
        <Script
          id="structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />

        {/* Google AdSense Script - loads after page becomes interactive */}
        {process.env.NEXT_PUBLIC_ADSENSE_CLIENT && (
          <script
            async
            src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${process.env.NEXT_PUBLIC_ADSENSE_CLIENT}`}
            crossOrigin="anonymous"
          />
        )}

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="preconnect" href="https://pagead2.googlesyndication.com" />

        {/* DNS prefetch for better performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        <link rel="dns-prefetch" href="//pagead2.googlesyndication.com" />

        {/* Additional SEO meta tags */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Water Calculator" />
        <meta name="application-name" content="Water Intake Calculator" />
        <meta name="msapplication-TileColor" content="#2563eb" />
        <meta name="theme-color" content="#2563eb" />

        {/* Favicon and app icons */}
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#2563eb" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-slate-900 dark:to-slate-800 min-h-screen transition-colors duration-200`}
      >
        <ThemeProvider>
          <div className="flex flex-col min-h-screen">
            <Navigation />
            <main className="flex-grow">
              {children}
            </main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
