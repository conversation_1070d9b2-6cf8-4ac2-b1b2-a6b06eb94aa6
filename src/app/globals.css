@import "tailwindcss";

/* Light theme variables */
:root {
  --background: #ffffff;
  --foreground: #111827;
  --card-background: #ffffff;
  --card-border: #e5e7eb;
  --input-background: #ffffff;
  --input-border: #d1d5db;
  --button-primary: #2563eb;
  --button-primary-hover: #1d4ed8;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-muted: #6b7280;
}

/* Dark theme variables */
.dark {
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card-background: #1e293b;
  --card-border: #334155;
  --input-background: #1e293b;
  --input-border: #475569;
  --button-primary: #3b82f6;
  --button-primary-hover: #2563eb;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom scrollbar for dark mode */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--text-muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
