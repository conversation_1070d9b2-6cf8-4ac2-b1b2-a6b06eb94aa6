'use client';

import { useState, useCallback } from 'react';
import Ad from '@/components/Ad';
import ThemeToggle from '@/components/ThemeToggle';

// TypeScript interfaces for form data and calculation
interface FormData {
  weight: number;
  exerciseHours: number;
  climate: 'normal' | 'hot';
}

interface CalculationResult {
  totalIntake: number;
  baseIntake: number;
  exerciseBonus: number;
  climateBonus: number;
}

export default function WaterIntakeCalculator() {
  // State management with TypeScript
  const [formData, setFormData] = useState<FormData>({
    weight: 70,
    exerciseHours: 0,
    climate: 'normal'
  });

  const [result, setResult] = useState<CalculationResult | null>(null);
  const [isCalculated, setIsCalculated] = useState<boolean>(false);

  // Water intake calculation function - Based on National Academy of Sciences recommendations
  const calculateWaterIntake = useCallback((data: FormData): CalculationResult => {
    // SCIENTIFIC BASE: National Academy of Sciences Adequate Intake (AI) recommendations
    // Men (19-30 years): 3.7L total water/day (includes food + beverages)
    // Women (19-30 years): 2.7L total water/day (includes food + beverages)
    // Approximately 80% from fluids, 20% from food

    // Base calculation using body weight correlation
    // Research shows: ~35-40ml per kg body weight for sedentary adults
    const baseIntake = data.weight * 0.035; // 35ml per kg (conservative estimate)

    // Exercise adjustment: Based on sweat loss research
    // Moderate exercise: ~0.5-1.0L additional per hour
    // We use 0.6L per hour as a moderate estimate
    const exerciseBonus = data.exerciseHours * 0.6;

    // Climate adjustment: Based on thermoregulation research
    // Hot/humid conditions increase fluid needs by 0.5-1.5L/day
    // We use 1.0L as a reasonable adjustment for hot climates
    const climateBonus = data.climate === 'hot' ? 1.0 : 0;

    // Total intake (this represents total fluid needs, not including water from food)
    const totalIntake = baseIntake + exerciseBonus + climateBonus;

    return {
      totalIntake: Math.round(totalIntake * 100) / 100, // Round to 2 decimal places
      baseIntake: Math.round(baseIntake * 100) / 100,
      exerciseBonus: Math.round(exerciseBonus * 100) / 100,
      climateBonus
    };
  }, []);

  // Handle form input changes
  const handleInputChange = (field: keyof FormData, value: number | string) => {
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);

    // Real-time calculation
    if (newFormData.weight > 0) {
      const calculationResult = calculateWaterIntake(newFormData);
      setResult(calculationResult);
      setIsCalculated(true);
    } else {
      setResult(null);
      setIsCalculated(false);
    }
  };

  // Handle calculate button click
  const handleCalculate = () => {
    if (formData.weight > 0) {
      const calculationResult = calculateWaterIntake(formData);
      setResult(calculationResult);
      setIsCalculated(true);
    }
  };

  return (
    <main className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        {/* Ad Space - Top */}
        <aside className="mb-6 w-full">
          <Ad adSlot="**********" className="w-full max-w-4xl mx-auto" />
        </aside>
        {/* Header */}
        <header className="text-center mb-8 relative">
          {/* Theme Toggle */}
          <div className="absolute top-0 right-0 mt-2 mr-2">
            <ThemeToggle />
          </div>

          <div className="pt-12">
            <h1 className="text-4xl sm:text-5xl font-bold text-gray-800 dark:text-gray-100 mb-4 transition-colors duration-200">
              💧 Water Intake Calculator
            </h1>
            <p className="text-lg text-gray-700 dark:text-gray-300 max-w-xl mx-auto transition-colors duration-200">
              Calculate your personalized daily water intake needs with our evidence-based hydration calculator.
              Uses National Academy of Sciences guidelines and scientific research to determine optimal fluid intake
              based on your weight, exercise routine, and climate conditions.
            </p>
            <div className="mt-4 flex flex-wrap justify-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <span className="bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full">Science-Based</span>
              <span className="bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full">Personalized</span>
              <span className="bg-purple-100 dark:bg-purple-900/30 px-3 py-1 rounded-full">Free Calculator</span>
            </div>
          </div>
        </header>

        {/* Calculator Form */}
        <section className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl dark:shadow-2xl p-6 sm:p-8 mb-8 transition-colors duration-200 border border-gray-100 dark:border-gray-700" aria-labelledby="calculator-heading">
          <h2 id="calculator-heading" className="sr-only">Water Intake Calculator Form</h2>
          <div className="space-y-6">
            {/* Weight Input */}
            <div>
              <label htmlFor="weight" className="block text-sm font-semibold text-gray-800 dark:text-gray-300 mb-2 transition-colors duration-200">
                Your Weight (kg) *
              </label>
              <input
                type="number"
                id="weight"
                min="1"
                max="300"
                step="0.1"
                value={formData.weight}
                onChange={(e) => handleInputChange('weight', parseFloat(e.target.value) || 0)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-400"
                placeholder="Enter your weight"
                required
              />
            </div>

            {/* Exercise Hours Input */}
            <div>
              <label htmlFor="exercise" className="block text-sm font-semibold text-gray-800 dark:text-gray-300 mb-2 transition-colors duration-200">
                Exercise Duration (hours/day)
              </label>
              <input
                type="number"
                id="exercise"
                min="0"
                max="12"
                step="0.5"
                value={formData.exerciseHours}
                onChange={(e) => handleInputChange('exerciseHours', parseFloat(e.target.value) || 0)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-400"
                placeholder="0"
              />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 transition-colors duration-200">Hours of physical activity per day (0.5 hour increments)</p>
            </div>

            {/* Climate Selection */}
            <div>
              <label htmlFor="climate" className="block text-sm font-semibold text-gray-800 dark:text-gray-300 mb-2 transition-colors duration-200">
                Climate Conditions
              </label>
              <select
                id="climate"
                value={formData.climate}
                onChange={(e) => handleInputChange('climate', e.target.value as 'normal' | 'hot')}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-all duration-200 text-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="normal">Normal Climate</option>
                <option value="hot">Hot Climate</option>
              </select>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 transition-colors duration-200">Select hot climate if you live in a warm environment</p>
            </div>

            {/* Calculate Button */}
            <button
              onClick={handleCalculate}
              disabled={formData.weight <= 0}
              className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white font-semibold py-4 px-6 rounded-lg hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg shadow-lg hover:shadow-xl"
            >
              Calculate Water Intake
            </button>
          </div>
        </section>

        {/* Results Display */}
        {isCalculated && result && (
          <section className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl shadow-lg dark:shadow-2xl p-6 sm:p-8 mb-8 border border-blue-100 dark:border-blue-800/30 transition-colors duration-200" aria-labelledby="results-heading">
            <h2 id="results-heading" className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4 text-center transition-colors duration-200">
              Your Daily Water Intake
            </h2>

            <div className="text-center mb-6">
              <div className="text-5xl font-bold text-blue-600 dark:text-blue-400 mb-2 transition-colors duration-200">
                {result.totalIntake}L
              </div>
              <p className="text-lg text-gray-700 dark:text-gray-300 transition-colors duration-200">
                Recommended daily intake: <strong className="text-gray-900 dark:text-gray-100">{result.totalIntake} liters</strong>
              </p>
            </div>

            {/* Breakdown */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 transition-colors duration-200">
                <div className="text-2xl font-semibold text-gray-900 dark:text-gray-200 transition-colors duration-200">{result.baseIntake}L</div>
                <div className="text-sm text-gray-700 dark:text-gray-400 transition-colors duration-200">Base Intake</div>
                <div className="text-xs text-gray-600 dark:text-gray-500 transition-colors duration-200">35ml per kg body weight</div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 transition-colors duration-200">
                <div className="text-2xl font-semibold text-orange-600 dark:text-orange-400 transition-colors duration-200">{result.exerciseBonus}L</div>
                <div className="text-sm text-gray-700 dark:text-gray-400 transition-colors duration-200">Exercise Addition</div>
                <div className="text-xs text-gray-600 dark:text-gray-500 transition-colors duration-200">+0.6L per hour</div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-100 dark:border-gray-700 transition-colors duration-200">
                <div className="text-2xl font-semibold text-red-600 dark:text-red-400 transition-colors duration-200">{result.climateBonus}L</div>
                <div className="text-sm text-gray-700 dark:text-gray-400 transition-colors duration-200">Climate Addition</div>
                <div className="text-xs text-gray-600 dark:text-gray-500 transition-colors duration-200">Hot climate +1.0L</div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-100 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-800/50 transition-colors duration-200">
              <p className="text-sm text-blue-800 dark:text-blue-200 transition-colors duration-200">
                📚 <strong>Scientific Basis:</strong> This calculation is based on National Academy of Sciences recommendations
                and peer-reviewed research on hydration needs. Values represent fluid intake recommendations (water + beverages).
              </p>
            </div>

            <div className="mt-4 p-4 bg-green-100 dark:bg-green-900/30 rounded-lg border border-green-200 dark:border-green-800/50 transition-colors duration-200">
              <p className="text-sm text-green-800 dark:text-green-200 transition-colors duration-200">
                💡 <strong>Hydration Tips:</strong> Spread intake throughout the day • Drink before you feel thirsty •
                Monitor urine color (pale yellow is ideal) • Increase intake during illness or high altitude
              </p>
            </div>
          </section>
        )}

        {/* FAQ Section */}
        <section className="mb-8" aria-labelledby="faq-heading">
          <h2 id="faq-heading" className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            <details className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors duration-200">
              <summary className="font-semibold text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400">
                How accurate is this water intake calculator?
              </summary>
              <p className="mt-2 text-gray-700 dark:text-gray-300">
                Our calculator uses evidence-based formulas from the National Academy of Sciences and Mayo Clinic guidelines.
                It provides scientifically accurate estimates, but individual needs may vary based on health conditions,
                medications, and other factors.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors duration-200">
              <summary className="font-semibold text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400">
                Should I drink more water if I exercise?
              </summary>
              <p className="mt-2 text-gray-700 dark:text-gray-300">
                Yes, physical activity increases fluid needs due to sweat loss. Our calculator adds approximately 0.6 liters
                per hour of exercise, based on research on sweat rates during moderate physical activity.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 transition-colors duration-200">
              <summary className="font-semibold text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400">
                Does climate affect water intake needs?
              </summary>
              <p className="mt-2 text-gray-700 dark:text-gray-300">
                Absolutely. Hot and humid climates increase fluid needs for thermoregulation. Our calculator adds 1.0 liter
                for hot climate conditions, based on research on fluid requirements in warm environments.
              </p>
            </details>
          </div>
        </section>

        {/* AdSense Ad Placement */}
        {isCalculated && (
          <aside className="w-full my-8">
            <Ad
              adSlot="**********"
              className="w-full max-w-2xl mx-auto"
            />
          </aside>
        )}

        {/* Footer Information */}
        <footer className="text-center text-gray-700 dark:text-gray-400 text-sm mt-12 transition-colors duration-200">
          <p className="mb-2">
            <strong className="text-gray-900 dark:text-gray-200">Medical Disclaimer:</strong> This calculator provides evidence-based estimates based on National Academy of Sciences
            guidelines and scientific research. Individual needs may vary based on health conditions, medications, pregnancy,
            breastfeeding, and other factors.
          </p>
          <p className="mb-2">
            <strong className="text-gray-900 dark:text-gray-200">When to consult a healthcare provider:</strong> Kidney disease, heart conditions, diabetes, or if taking
            medications that affect fluid balance.
          </p>
          <p>
            💧 <strong className="text-gray-900 dark:text-gray-200">Sources:</strong> National Academy of Sciences • Mayo Clinic • Peer-reviewed hydration research
          </p>
        </footer>
      </div>
    </main>
  );
}
